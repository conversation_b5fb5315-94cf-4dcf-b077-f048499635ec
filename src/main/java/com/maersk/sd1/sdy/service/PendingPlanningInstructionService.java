package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.YardRepository;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PendingPlanningInstructionService {

    private static final Logger logger = LogManager.getLogger(PendingPlanningInstructionService.class);

    private final CatalogRepository catalogRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final YardRepository yardRepository;

    @Transactional
    public List<PendingPlanningInstructionOutput> getPendingPlanningInstructions(PendingPlanningInstructionInput.Input input) {

        Catalog catOnHold = catalogRepository.findCatalogByParentAndCode("EMI", "OH").orElse(null);
        Catalog catInProgress = catalogRepository.findCatalogByParentAndCode("EMI", "IP").orElse(null);
        Catalog catToBeAttended = catalogRepository.findCatalogByParentAndCode("EMI", "DP").orElse(null);



        List<String> aliases = List.of("sd1_equipment_category_container", "43081", "43083");
        List<Object[]> results = catalogRepository.findIdsByAliases(aliases);
        Map<String, Integer> aliasToId = new HashMap<>();
        for (Object[] result : results) {
            aliasToId.put((String) result[0], (Integer) result[1]);
        }
        Map<String, Integer> catalogIds = new HashMap<>();
        catalogIds.put("isContainer", aliasToId.get("sd1_equipment_category_container"));
        catalogIds.put("isGateOut", aliasToId.get("43083"));
        catalogIds.put("isEmpty", aliasToId.get("43081"));
        logger.info("Catalog IDs: {}", catalogIds);


        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitIdByBusinessUnitId(input.getLocalBusinessUnitId());
        Integer yardId = yardRepository.findYardIdByBusinessUnitId(input.getLocalBusinessUnitId());





        return null;




    }

}
